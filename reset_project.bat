@echo off
echo ========================================
echo Flash Builder项目完全重置工具
echo ========================================
echo.

echo 步骤1: 关闭Flash Builder
echo 请先关闭Flash Builder，然后按任意键继续...
pause > nul

echo.
echo 步骤2: 清理项目缓存文件...

REM 删除可能导致问题的缓存文件
if exist ".metadata" (
    echo 删除.metadata目录...
    rmdir /s /q ".metadata"
)

if exist ".settings" (
    echo 删除.settings目录...
    rmdir /s /q ".settings"
)

REM 清理编译输出
if exist "bin-debug" (
    echo 清理bin-debug目录...
    rmdir /s /q "bin-debug"
    mkdir "bin-debug"
)

REM 删除可能的临时文件
if exist "*.tmp" del /q "*.tmp"
if exist "*.log" del /q "*.log"

echo.
echo 步骤3: 重新创建项目配置...

REM 重新创建.settings目录
mkdir ".settings"

REM 创建新的资源配置文件
echo #%date% %time% > ".settings\org.eclipse.core.resources.prefs"
echo eclipse.preferences.version=1 >> ".settings\org.eclipse.core.resources.prefs"
echo encoding/^<project^>=utf-8 >> ".settings\org.eclipse.core.resources.prefs"

REM 重新创建必要的目录
if not exist "assets" (
    echo 创建assets目录结构...
    mkdir "assets"
    mkdir "assets\xmls"
    mkdir "assets\swfs"
    mkdir "assets\bitmaps"
    mkdir "assets\sounds"
    mkdir "assets\modules"
)

echo.
echo ========================================
echo 重置完成！请按照以下步骤操作：
echo ========================================
echo.
echo 1. 启动Flash Builder
echo 2. 创建新的工作空间（路径不包含中文）
echo 3. 选择 File -^> Import -^> General -^> Existing Projects into Workspace
echo 4. 选择当前目录导入项目
echo 5. 项目名称应该显示为 "PirateGameDesktop"
echo.
echo 如果仍有问题，请尝试：
echo - 重启Flash Builder
echo - 使用英文路径的工作空间
echo - 检查AIR SDK是否正确安装
echo.
pause
