@echo off
echo 正在重置Flash Builder项目配置...

REM 删除可能导致问题的缓存文件
if exist ".metadata" (
    echo 删除.metadata目录...
    rmdir /s /q ".metadata"
)

if exist ".settings" (
    echo 删除.settings目录...
    rmdir /s /q ".settings"
)

REM 清理编译输出
if exist "bin-debug\*.swf" (
    echo 清理编译输出文件...
    del /q "bin-debug\*.swf"
)

REM 重新创建必要的目录
if not exist "assets" (
    echo 创建assets目录...
    mkdir "assets"
    mkdir "assets\xmls"
    mkdir "assets\swfs"
    mkdir "assets\bitmaps"
    mkdir "assets\sounds"
    mkdir "assets\modules"
)

echo.
echo 项目重置完成！
echo.
echo 请按照以下步骤重新导入项目：
echo 1. 在Flash Builder中关闭当前项目
echo 2. 选择 File -^> Import -^> General -^> Existing Projects into Workspace
echo 3. 选择项目目录并导入
echo 4. 项目名称应该显示为 "PirateGameDesktop"
echo.
pause
