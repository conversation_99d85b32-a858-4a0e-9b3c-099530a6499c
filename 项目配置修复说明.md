# Flash Builder项目配置修复说明

## 问题描述
在Flash Builder中出现错误："Path must include project and resource name: /web热海"

这个错误是由于项目名称包含中文字符导致的路径解析问题。

## 已执行的修复操作

### 1. 修改项目名称
已将`.project`文件中的项目名称从`web热海`改为`PirateGameDesktop`

### 2. 项目重新导入步骤

请按照以下步骤重新导入项目：

#### 方法一：刷新项目
1. 在Flash Builder中右键点击项目
2. 选择"Refresh"刷新项目
3. 如果仍有问题，继续使用方法二

#### 方法二：重新导入项目
1. 在Flash Builder中关闭当前项目
2. 选择 File -> Import -> General -> Existing Projects into Workspace
3. 选择项目根目录：`g:\pirate\rxhzw\scripts`
4. 项目名称现在应该显示为"PirateGameDesktop"
5. 点击"Finish"完成导入

#### 方法三：创建新的工作空间（推荐）
1. 创建一个新的工作空间，路径不包含中文字符
2. 将项目文件复制到新的位置
3. 在新工作空间中导入项目

### 3. 验证项目配置

导入成功后，请验证以下配置：

- **项目类型**：应该显示为AIR Desktop项目
- **主应用程序文件**：src/Main.mxml
- **输出目录**：bin-debug
- **源码路径**：包含src目录和根目录

### 4. 编译测试

1. 右键点击项目，选择"Clean Project"
2. 然后选择"Build Project"
3. 检查是否有编译错误

## 常见问题解决

### 如果仍然出现路径错误：
1. 确保工作空间路径不包含中文字符
2. 确保项目路径不包含中文字符
3. 重启Flash Builder
4. 清理工作空间缓存

### 如果找不到Main类：
1. 检查源码路径配置是否包含根目录
2. 确保Main.as文件在项目根目录中
3. 检查import语句是否正确

### 如果AIR应用无法启动：
1. 检查AIR SDK版本是否兼容
2. 确保AIR应用描述符配置正确
3. 检查资源文件路径

## 下一步操作

修复完成后，你就可以：
1. 正常编译AIR项目
2. 运行和调试应用程序
3. 打包发布AIR应用

如果遇到其他问题，请检查Flash Builder的错误日志获取更详细的信息。
