package game
{
	import flash.filesystem.File;
	import Main;
	
	/**
	 * AIR环境下的Main类适配器
	 * 继承原有的Main类，重写资源路径相关方法以适配AIR环境
	 */
	public class AIRMain extends Main
	{
		public function AIRMain()
		{
			super();
		}
		
		/**
		 * 重写getMainAssetsPath方法，返回AIR应用的资源路径
		 */
		override protected function getMainAssetsPath():String
		{
			try
			{
				// 获取应用程序目录
				var appDir:File = File.applicationDirectory;
				
				// 检查是否存在assets目录
				var assetsDir:File = appDir.resolvePath("assets");
				if (assetsDir.exists)
				{
					return assetsDir.url;
				}
				
				// 如果assets目录不存在，尝试使用应用程序目录
				return appDir.url;
			}
			catch (error:Error)
			{
				trace("获取资源路径时发生错误：" + error.message);
				// 返回相对路径作为备选方案
				return "./";
			}
		}
		
		/**
		 * 重写初始化方法，添加AIR特定的初始化逻辑
		 */
		override protected function init(event:*):void
		{
			trace("AIR环境下初始化游戏...");
			
			// 设置AIR特定的配置
			setupAIRConfig();
			
			// 调用父类的初始化方法
			super.init(event);
		}
		
		/**
		 * 设置AIR特定的配置
		 */
		private function setupAIRConfig():void
		{
			try
			{
				// 设置调试资源基础URL（如果需要）
				if (debugAssetBaseUrl == null)
				{
					// 在AIR环境中，可以使用应用程序目录作为基础路径
					var appDir:File = File.applicationDirectory;
					debugAssetBaseUrl = appDir.url + "/";
				}
				
				trace("AIR资源基础路径设置为：" + debugAssetBaseUrl);
			}
			catch (error:Error)
			{
				trace("设置AIR配置时发生错误：" + error.message);
			}
		}
		
		/**
		 * 重写资源加载相关方法（如果需要）
		 */
		override protected function isConnetSever():Boolean
		{
			// 在AIR环境中，可能需要不同的连接逻辑
			// 这里先使用父类的实现
			return super.isConnetSever();
		}
	}
}
