package mmo.utils.dataLoader.loadTypes
{
   import flash.display.Bitmap;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.utils.ByteArray;
   import mmo.Core;
   import util.LiuXiang;
   
   public class ImageLoader extends GroupLoader
   {
      private var loader:Loader;
      
      public function ImageLoader()
      {
         super();
      }
      
      override protected function completeStepHandler(param1:Event) : void
      {
         super.completeStepHandler(param1);
         this.loader = new Loader();
         this.loader.contentLoaderInfo.addEventListener("complete",this.completeHandler);
         this.loader.contentLoaderInfo.addEventListener("ioError",ioErrorHandler);
         var _loc3_:LoaderContext = new LoaderContext(false,ApplicationDomain.currentDomain);
         var _loc2_:ByteArray = urlLoader.data as ByteArray;
         if(Main.isEncrypted(_loc2_))
         {
            Main.getEncryption().decryptBytes(_loc2_);
         }
         if(Core.cacheModule == 1)
         {
            if(RWCache.instance().checkFile(this.loadUrl) == false)
            {
               RWCache.instance().addCach(this.loadUrl,_loc2_);
            }
         }
         if(Main.isEncryptedWM(_loc2_))
         {
            this.loader.loadBytes(LiuXiang.decode(_loc2_),_loc3_);
         }
         else
         {
            this.loader.loadBytes(_loc2_,_loc3_);
         }
      }
      
      override protected function completeHandler(param1:Event) : void
      {
         this._data = Bitmap(this.loader.content).bitmapData;
         super.completeHandler(param1);
      }
      
      override public function clear() : void
      {
         super.clear();
         if(this.loader)
         {
            this.loader.contentLoaderInfo.removeEventListener("complete",this.completeHandler);
            this.loader.contentLoaderInfo.removeEventListener("ioError",ioErrorHandler);
            this.loader = null;
         }
      }
   }
}

