package
{
   import game.manager.LoggerManager;
   import game.manager.XmlManager;
   import mmo.Config;
   
   public class ReleaseMain extends Main
   {
      public function ReleaseMain()
      {
         super();
         Config.noToken = true;
         Config.noTrace = true;
         Config.clientDebug = false;
         Config.serverDebug = false;
         Config.battelDebug = false;
         LoggerManager.isRun = false;
         XmlManager.isRelease = true;
         skipLoading = false;
      }
   }
}

