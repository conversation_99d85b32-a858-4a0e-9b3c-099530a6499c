# AIR项目资源目录结构说明

## 当前项目结构

```
项目根目录/
├── src/                          # AIR项目源码目录
│   ├── Main.mxml                # AIR应用主入口文件
│   ├── Main-app.xml             # AIR应用描述符
│   └── game/                    # 游戏相关类
│       ├── GameMain.as          # 游戏主容器类
│       └── AIRMain.as           # AIR适配的Main类
├── bin-debug/                   # 编译输出目录
├── assets/                      # 游戏资源目录（需要创建）
│   ├── xmls/                    # XML配置文件
│   ├── swfs/                    # SWF资源文件
│   ├── bitmaps/                 # 图片资源
│   ├── sounds/                  # 音频资源
│   └── modules/                 # 模块文件
├── 原有游戏代码文件...           # 所有原有的.as文件
└── _assets/                     # 原有资源目录
    └── assets.swf
```

## 需要进行的资源整理

### 1. 创建assets目录结构
需要在项目根目录创建assets目录，并按照游戏代码中的路径结构组织资源：

- `assets/xmls/` - 存放所有XML配置文件
- `assets/swfs/` - 存放SWF资源文件  
- `assets/bitmaps/` - 存放图片资源
- `assets/sounds/` - 存放音频资源
- `assets/modules/` - 存放模块文件

### 2. 资源路径配置
AIRMain类会自动检测并设置正确的资源路径：
- 优先使用应用程序目录下的assets文件夹
- 如果assets不存在，使用应用程序根目录
- 支持相对路径作为备选方案

### 3. 编译配置
项目已配置为包含原有游戏代码：
- 源码路径包含src目录和根目录
- 支持导入所有原有的ActionScript类
- 保持原有的包结构和依赖关系

## 下一步操作建议

1. **创建资源目录**：在项目根目录创建assets文件夹及子目录
2. **复制游戏资源**：将原有的游戏资源文件复制到对应的assets子目录中
3. **测试编译**：使用Flash Builder编译项目，检查是否有错误
4. **调试运行**：运行AIR应用，测试游戏是否能正常启动和运行

## 注意事项

- AIR应用的安全模型与Flash Player不同，某些网络访问可能需要额外配置
- 文件系统访问需要在AIR应用描述符中声明相应权限
- 资源加载路径在AIR环境中使用file://协议
