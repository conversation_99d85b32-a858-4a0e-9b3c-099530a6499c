@echo off
echo ========================================
echo 创建全新的AIR项目
echo ========================================
echo.

set PROJECT_NAME=PirateGameDesktop
set NEW_DIR=..\%PROJECT_NAME%_New

echo 正在创建新的项目目录: %NEW_DIR%
if exist "%NEW_DIR%" (
    echo 删除现有的新项目目录...
    rmdir /s /q "%NEW_DIR%"
)

mkdir "%NEW_DIR%"
mkdir "%NEW_DIR%\src"
mkdir "%NEW_DIR%\src\game"
mkdir "%NEW_DIR%\assets"
mkdir "%NEW_DIR%\assets\xmls"
mkdir "%NEW_DIR%\assets\swfs"
mkdir "%NEW_DIR%\assets\bitmaps"
mkdir "%NEW_DIR%\assets\sounds"
mkdir "%NEW_DIR%\assets\modules"
mkdir "%NEW_DIR%\bin-debug"
mkdir "%NEW_DIR%\libs"

echo.
echo 复制源代码文件...

REM 复制所有ActionScript文件
xcopy "*.as" "%NEW_DIR%\" /s /y /q
xcopy "com" "%NEW_DIR%\com\" /s /y /q
xcopy "game" "%NEW_DIR%\game\" /s /y /q
xcopy "mmo" "%NEW_DIR%\mmo\" /s /y /q
xcopy "util" "%NEW_DIR%\util\" /s /y /q
xcopy "mvc" "%NEW_DIR%\mvc\" /s /y /q
xcopy "org" "%NEW_DIR%\org\" /s /y /q
xcopy "fl" "%NEW_DIR%\fl\" /s /y /q
xcopy "mx" "%NEW_DIR%\mx\" /s /y /q
xcopy "flashx" "%NEW_DIR%\flashx\" /s /y /q

REM 复制所有模块目录
for /d %%i in (*module) do (
    if exist "%%i" xcopy "%%i" "%NEW_DIR%\%%i\" /s /y /q
)

REM 复制其他重要目录
if exist "activity" xcopy "activity" "%NEW_DIR%\activity\" /s /y /q
if exist "battleConfig" xcopy "battleConfig" "%NEW_DIR%\battleConfig\" /s /y /q
if exist "controler" xcopy "controler" "%NEW_DIR%\controler\" /s /y /q
if exist "display" xcopy "display" "%NEW_DIR%\display\" /s /y /q
if exist "libs" xcopy "libs" "%NEW_DIR%\libs\" /s /y /q

REM 复制AIR项目文件
copy "src\Main.mxml" "%NEW_DIR%\src\"
copy "src\Main-app.xml" "%NEW_DIR%\src\"
copy "src\game\*.as" "%NEW_DIR%\src\game\"

echo.
echo 创建新的项目配置文件...

REM 创建.project文件
echo ^<?xml version="1.0" encoding="UTF-8"?^> > "%NEW_DIR%\.project"
echo ^<projectDescription^> >> "%NEW_DIR%\.project"
echo 	^<name^>%PROJECT_NAME%^</name^> >> "%NEW_DIR%\.project"
echo 	^<comment^>^</comment^> >> "%NEW_DIR%\.project"
echo 	^<projects^>^</projects^> >> "%NEW_DIR%\.project"
echo 	^<buildSpec^> >> "%NEW_DIR%\.project"
echo 		^<buildCommand^> >> "%NEW_DIR%\.project"
echo 			^<name^>com.adobe.flexbuilder.project.flexbuilder^</name^> >> "%NEW_DIR%\.project"
echo 		^</buildCommand^> >> "%NEW_DIR%\.project"
echo 		^<buildCommand^> >> "%NEW_DIR%\.project"
echo 			^<name^>com.adobe.flexbuilder.project.apollobuilder^</name^> >> "%NEW_DIR%\.project"
echo 		^</buildCommand^> >> "%NEW_DIR%\.project"
echo 	^</buildSpec^> >> "%NEW_DIR%\.project"
echo 	^<natures^> >> "%NEW_DIR%\.project"
echo 		^<nature^>com.adobe.flexbuilder.project.apollonature^</nature^> >> "%NEW_DIR%\.project"
echo 		^<nature^>com.adobe.flexbuilder.project.flexnature^</nature^> >> "%NEW_DIR%\.project"
echo 		^<nature^>com.adobe.flexbuilder.project.actionscriptnature^</nature^> >> "%NEW_DIR%\.project"
echo 	^</natures^> >> "%NEW_DIR%\.project"
echo ^</projectDescription^> >> "%NEW_DIR%\.project"

REM 复制其他配置文件
copy ".actionScriptProperties" "%NEW_DIR%\"
copy ".flexProperties" "%NEW_DIR%\"

echo.
echo ========================================
echo 新项目创建完成！
echo ========================================
echo.
echo 项目位置: %NEW_DIR%
echo 项目名称: %PROJECT_NAME%
echo.
echo 请按照以下步骤导入新项目：
echo 1. 启动Flash Builder
echo 2. 选择 File -^> Import -^> General -^> Existing Projects into Workspace
echo 3. 选择目录: %CD%\%NEW_DIR%
echo 4. 导入项目
echo.
pause
