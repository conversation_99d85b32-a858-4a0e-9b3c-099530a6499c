package
{
   import fl.controls.CheckBox;
   import flash.display.Bitmap;
   import flash.display.Loader;
   import flash.display.LoaderInfo;
   import flash.display.Sprite;
   import flash.events.ErrorEvent;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.filesystem.File;
   import flash.filesystem.FileStream;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.Timer;
   import mmo.Core;
   
   public class Login extends Sprite
   {
      private var loader:Loader;
      
      private var loginman:Sprite = new LoginMain();
      
      private var btlogin:BtnIn = new BtnIn();
      
      private var username:TextField;
      
      private var password:TextField;
      
      private var index:TextField = new TextField();
      
      private var reg:TextField = new TextField();
      
      private var clear:TextField = new TextField();
      
      private var tmpurlloader:URLLoader;
      
      private var messloader:URLLoader;
      
      private var mystyle:TextFormat;
      
      private var appkey:String = "完美海贼王v1.0.0";
      
      private var serverid:String = "";
      
      private var gameid:* = 0;
      
      private var sverdatalist:Array = [];
      
      private var chklist:Array = [];
      
      private var serverkey:String = "";
      
      private var status:String = "";
      
      private var mess:String = "";
      
      private var fileChedk:File = new File();
      
      private var saveuser:FileStream = new FileStream();
      
      private var selectname:String = "";
      
      private var fps:int = 45;
      
      private var imagelist:Array = [];
      
      private var img:Array = [];
      
      private var backpic:Bitmap = new Bitmap();
      
      private var refer:Timer = new Timer(2000);
      
      private var filedelete:File = new File();
      
      private var defname:String = "";
      
      private var fp45:CheckBox = new CheckBox();
      
      private var fp60:CheckBox = new CheckBox();
      
      public function Login()
      {
         super();
         username = new TextField();
         password = new TextField();
         tmpurlloader = new URLLoader();
         tmpurlloader.load(new URLRequest("http://www.superhzw.cn:9528/key.xml"));
         tmpurlloader.addEventListener("complete",loadglobalxml);
         messloader = new URLLoader();
         messloader.load(new URLRequest("http://www.superhzw.cn:9528/notice.xml"));
         messloader.addEventListener("complete",loadNotice);
         this.addChild(loginman);
         this.addChild(btlogin);
         this.addChild(fp45);
         this.addChild(fp60);
         var _loc3_:TextFormat = new TextFormat();
         _loc3_.color = 65280;
         fp45.x = 331;
         fp45.y = 518;
         fp45.label = "45帧";
         fp45.textField.background = true;
         fp45.name = "_fp1";
         fp60.x = 331;
         fp60.y = 538;
         fp60.label = "60帧";
         fp60.textField.background = true;
         fp60.name = "_fp2";
         btlogin.name = "login";
         btlogin.x = 740;
         btlogin.y = 515;
         username.name = "_user";
         password.name = "_pass";
         this.addChild(username);
         username.x = 367;
         username.y = 398;
         username.width = 340;
         username.height = 38;
         username.type = "input";
         password.type = "input";
         this.addChild(password);
         password.x = 367;
         password.y = 458;
         password.width = 330;
         password.height = 20;
         password.displayAsPassword = true;
         index.selectable = false;
         reg.selectable = false;
         index.htmlText = "<font face=\'微软雅黑\' size=\'24\' color=\'#ffdc65\'><a href=\'http://" + serverid + "/\'>首页</a></font>";
         reg.htmlText = "<font face=\'微软雅黑\' size=\'24\' color=\'#ffdc65\'><a href=\'http://" + serverid + "/\'>游戏注册</a></font>";
         clear.htmlText = "<font face=\'微软雅黑\' size=\'12\' color=\'#ffffff\'><u>清空缓存</u></font>";
         index.width = 1024;
         clear.name = "_clear";
         this.addChild(index);
         this.addChild(reg);
         this.addChild(clear);
         index.x = 347;
         index.y = 77;
         reg.x = 457;
         reg.y = 77;
         clear.x = 900;
         clear.y = 544;
         clear.selectable = false;
         fp45.selected = true;
         var _loc1_:Array = [4,2];
         var _loc2_:Object = {
            "thickness":1,
            "color":0,
            "alpha":1,
            "pixelHinting":true,
            "scaleMode":"normal",
            "caps":"none",
            "joints":"miter",
            "miterLimit":3,
            "dashes":_loc1_
         };
         mystyle = new TextFormat();
         mystyle.color = 10449413;
         mystyle.font = "Arial";
         mystyle.size = 30;
         username.defaultTextFormat = mystyle;
         password.defaultTextFormat = mystyle;
         this.addEventListener("click",processCK);
      }
      
      private function loadglobalxml(param1:Event) : *
      {
         tmpurlloader.removeEventListener("complete",loadglobalxml);
         loadcachover(XML(param1.target.data));
      }
      
      private function loadNotice(param1:Event) : *
      {
         messloader.removeEventListener("complete",loadNotice);
         initnoteice(XML(param1.target.data));
      }
      
      private function loadImage() : *
      {
         var _loc1_:Loader = new Loader();
         _loc1_.load(new URLRequest("http://www.superhzw.cn:9528/" + img.shift()));
         _loc1_.contentLoaderInfo.addEventListener("complete",loadOver);
      }
      
      private function loadOver(param1:Event) : *
      {
         imagelist.push(LoaderInfo(param1.target).content as Bitmap);
         backpic.bitmapData = Bitmap(LoaderInfo(param1.target).content).bitmapData;
         if(img.length > 1)
         {
            loadImage();
         }
      }
      
      private function referPic(param1:Event) : *
      {
         var _loc2_:int = 0;
         if(imagelist.length > 0)
         {
            _loc2_ = Math.random() * imagelist.length;
            backpic.bitmapData = Bitmap(imagelist[_loc2_]).bitmapData;
         }
      }
      
      private function initnoteice(param1:XML) : *
      {
         var _loc16_:* = param1.group.(@id == appkey);
         var _loc12_:String = _loc16_.images.@src;
         img = _loc12_.split(";");
         loadImage();
         refer.addEventListener("timer",referPic);
         refer.start();
         var _loc2_:String = _loc16_.noteice.(@id == 1).@mess;
         var _loc4_:String = _loc16_.noteice.(@id == 1).@src;
         var _loc18_:TextField = new TextField();
         _loc18_.selectable = false;
         _loc18_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc4_ + "\'>" + _loc2_ + "</a></font>";
         _loc18_.x = 684;
         _loc18_.y = 155;
         _loc18_.width = 260;
         _loc18_.height = 24;
         this.addChild(_loc18_);
         var _loc9_:String = _loc16_.noteice.(@id == 2).@mess;
         var _loc3_:String = _loc16_.noteice.(@id == 2).@src;
         var _loc24_:TextField = new TextField();
         _loc24_.selectable = false;
         _loc24_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc3_ + "\'>" + _loc9_ + "</a></font>";
         _loc24_.x = 684;
         _loc24_.y = 185;
         _loc24_.width = 260;
         _loc24_.height = 24;
         this.addChild(_loc24_);
         var _loc8_:String = _loc16_.noteice.(@id == 3).@mess;
         var _loc10_:String = _loc16_.noteice.(@id == 3).@src;
         var _loc22_:TextField = new TextField();
         _loc22_.selectable = false;
         _loc22_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc10_ + "\'>" + _loc8_ + "</a></font>";
         _loc22_.x = 684;
         _loc22_.y = 215;
         _loc22_.width = 260;
         _loc22_.height = 24;
         this.addChild(_loc22_);
         var _loc7_:String = _loc16_.noteice.(@id == 4).@mess;
         var _loc6_:String = _loc16_.noteice.(@id == 4).@src;
         var _loc20_:TextField = new TextField();
         _loc20_.selectable = false;
         _loc20_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc6_ + "\'>" + _loc7_ + "</a></font>";
         _loc20_.x = 684;
         _loc20_.y = 245;
         _loc20_.width = 260;
         _loc20_.height = 24;
         this.addChild(_loc20_);
         var _loc5_:String = _loc16_.noteice.(@id == 5).@mess;
         var _loc14_:String = _loc16_.noteice.(@id == 5).@src;
         var _loc19_:TextField = new TextField();
         _loc19_.selectable = false;
         _loc19_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc14_ + "\'>" + _loc5_ + "</a></font>";
         _loc19_.x = 684;
         _loc19_.y = 275;
         _loc19_.width = 260;
         _loc19_.height = 24;
         this.addChild(_loc19_);
         var _loc15_:String = _loc16_.noteice.(@id == 6).@mess;
         var _loc11_:String = _loc16_.noteice.(@id == 6).@src;
         var _loc23_:TextField = new TextField();
         _loc23_.selectable = false;
         _loc23_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc11_ + "\'>" + _loc15_ + "</a></font>";
         _loc23_.x = 684;
         _loc23_.y = 305;
         _loc23_.width = 260;
         _loc23_.height = 24;
         this.addChild(_loc23_);
         var _loc13_:String = _loc16_.noteice.(@id == 7).@mess;
         var _loc17_:String = _loc16_.noteice.(@id == 7).@src;
         var _loc21_:TextField = new TextField();
         _loc21_.selectable = false;
         _loc21_.htmlText = "<font color=\'#ffffff\' ><a href=\'" + _loc17_ + "\'>" + _loc13_ + "</a></font>";
         _loc21_.x = 684;
         _loc21_.y = 335;
         _loc21_.width = 260;
         _loc21_.height = 24;
         this.addChild(_loc21_);
         this.addChild(backpic);
         backpic.x = 330;
         backpic.y = 130;
      }
      
      private function loadcachover(param1:XML) : void
      {
         var _loc2_:Object = null;
         var _loc3_:CheckBox = null;
         var _loc6_:XML = XML(param1);
         var _loc5_:int = 0;
         for each(var _loc4_ in _loc6_.children())
         {
            if(_loc4_.@id == appkey)
            {
               _loc5_++;
               _loc2_ = {};
               _loc2_.sid = String(_loc4_.@serverid);
               _loc2_.serverid = String(_loc4_.@server);
               _loc2_.zonename = String(_loc4_.@zonename);
               _loc2_.cacheModule = String(_loc4_.sources.@type * 1);
               _loc2_.cachlen = String(_loc4_.sources.@len * 1);
               _loc2_.len = String(_loc4_.sources.@len * 1);
               _loc2_.serverid = String(_loc4_.@server);
               _loc2_.serverkey = String(_loc4_.@keyfile);
               _loc2_.status = String(_loc4_.@status);
               _loc2_.mess = String(_loc4_.@mess);
               _loc2_.ver = String(_loc4_.@version);
               _loc2_.kyes = String(_loc4_.@kyes);
               _loc2_.gameid = String(_loc4_.@game);
               _loc2_.def = String(_loc4_.@def);
               this.gameid = String(_loc4_.@game);
               sverdatalist.push(_loc2_);
               if(_loc2_.def == "1")
               {
                  defname = String(_loc4_.@zonename);
               }
               _loc3_ = new CheckBox();
               _loc3_.label = _loc2_.zonename;
               _loc3_.textField.background = true;
               _loc3_.textField.backgroundColor = 65280;
               _loc3_.width = 150;
               _loc3_.y = 450 + _loc5_ * 30;
               _loc3_.name = _loc2_.zonename;
               chklist.push(_loc3_);
               this.addChild(_loc3_);
            }
         }
         checkInit();
      }
      
      private function selectQY(param1:String) : void
      {
         var _loc3_:String = null;
         var _loc2_:Object = getServer(param1);
         if(_loc2_)
         {
            Core.cacheModule = _loc2_.cacheModule * 1;
            Core.cachlen = _loc2_.len * 1;
            serverid = _loc2_.serverid;
            Core.serverkey = _loc2_.serverkey;
            status = _loc2_.status;
            mess = _loc2_.mess;
            _loc3_ = _loc2_.ver;
            Core.kyes = _loc2_.kyes;
            selectname = param1;
            gameid = _loc2_.gameid;
            index.htmlText = "<font face=\'微软雅黑\' size=\'24\' color=\'#ffdc65\'><a href=\'http://" + serverid + "/\'>首页</a></font>";
            reg.htmlText = "<font face=\'微软雅黑\' size=\'24\' color=\'#ffdc65\'><a href=\'http://" + serverid + "/\'>游戏注册</a></font>";
         }
      }
      
      private function getServer(param1:String) : Object
      {
         for each(var _loc2_ in sverdatalist)
         {
            if(_loc2_.zonename == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function processCK(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc2_:URLVariables = null;
         var _loc4_:URLRequest = null;
         var _loc3_:String = param1.target.name;
         if(param1.target as CheckBox)
         {
            _loc5_ = 0;
            while(_loc5_ < chklist.length)
            {
               CheckBox(chklist[_loc5_]).selected = false;
               _loc5_++;
            }
            CheckBox(param1.target).selected = true;
            selectQY(_loc3_);
         }
         trace(_loc3_);
         if(_loc3_ == "_fp2")
         {
            fp60.selected = true;
            fp45.selected = false;
            fps = 60;
            Core.frame = 60;
         }
         if(_loc3_ == "_fp1")
         {
            fp45.selected = true;
            fp60.selected = false;
            fps = 45;
            Core.frame = 45;
         }
         if(_loc3_ == "login")
         {
            _loc2_ = new URLVariables();
            _loc2_.Uname = username.text;
            _loc2_.Upass = password.text;
            _loc4_ = new URLRequest("http://" + serverid + "/WebRes/appLoginPC.php?Action=Login");
            _loc4_.data = _loc2_;
            _loc4_.method = "POST";
            tmpurlloader = new URLLoader();
            tmpurlloader.load(_loc4_);
            tmpurlloader.dataFormat = "binary";
            tmpurlloader.addEventListener("complete",logininfo);
            tmpurlloader.addEventListener("error",logininfoError);
         }
         if(_loc3_ == "_clear")
         {
            try
            {
               filedelete.nativePath = Core.localPath;
               filedelete.deleteDirectory(true);
            }
            catch(e:Error)
            {
               trace("delete file false");
            }
         }
      }
      
      private function checkInit() : *
      {
         var _loc1_:String = null;
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         fileChedk.nativePath = Core.gamePath + "/user.ini";
         var _loc3_:Boolean = Boolean(fileChedk.exists);
         if(_loc3_)
         {
            saveuser.open(new File(Core.gamePath + "/user.ini"),"read");
            _loc1_ = saveuser.readUTFBytes(saveuser.bytesAvailable);
            _loc2_ = _loc1_.split(":");
            username.text = _loc2_[0];
            password.text = _loc2_[1];
            selectname = _loc2_[2];
            selectQY(selectname);
            ZoneSelect(selectname);
            if(_loc2_[3] == 45)
            {
               fp45.selected = true;
               fp60.selected = false;
               Core.frame = 45;
            }
            else
            {
               fp45.selected = false;
               fp60.selected = true;
               Core.frame = 60;
            }
            saveuser.close();
         }
         else
         {
            _loc4_ = 0;
            while(_loc4_ < chklist.length)
            {
               CheckBox(chklist[_loc4_]).selected = false;
               if(CheckBox(chklist[_loc4_]).label == defname)
               {
                  CheckBox(chklist[_loc4_]).selected = true;
                  selectQY(defname);
               }
               _loc4_++;
            }
         }
      }
      
      private function ZoneSelect(param1:String) : void
      {
         var _loc2_:int = 0;
         _loc2_ = 0;
         while(_loc2_ < chklist.length)
         {
            if(CheckBox(chklist[_loc2_]).name == param1)
            {
               CheckBox(chklist[_loc2_]).selected = true;
            }
            else
            {
               CheckBox(chklist[_loc2_]).selected = false;
            }
            _loc2_++;
         }
      }
      
      private function startMain() : void
      {
         loader = new Loader();
         var _loc1_:LoaderContext = new LoaderContext(false,ApplicationDomain.currentDomain);
         _loc1_.allowCodeImport = true;
         loader.load(new URLRequest(Core.serverkey),_loc1_);
         loader.contentLoaderInfo.addEventListener("complete",loadCompleteFun);
         loader.contentLoaderInfo.addEventListener("ioError",fileError);
      }
      
      private function loadCompleteFun(param1:Event) : void
      {
         this.parent.removeChild(this);
         this.removeEventListener("complete",loadCompleteFun);
         var _loc3_:* = param1.currentTarget.content;
         var _loc2_:Sprite = Sprite(_loc3_);
         Core.main.addChild(_loc2_);
      }
      
      private function fileError(param1:IOErrorEvent) : *
      {
         trace(param1.text);
      }
      
      private function logininfoError(param1:ErrorEvent) : void
      {
         trace(param1.text);
      }
      
      private function logininfo(param1:Event) : void
      {
         var _loc5_:Boolean = false;
         var _loc4_:TextField = null;
         tmpurlloader.removeEventListener("complete",logininfo);
         var _loc2_:String = param1.target.data;
         var _loc3_:Array = _loc2_.split(":");
         if(_loc3_[1] == "y")
         {
            trace(_loc3_[0]);
            Core.pid = _loc3_[2];
            Core.skey = _loc3_[3];
            Core.xmlstr = _loc3_[4];
            startMain();
            fileChedk.nativePath = Core.gamePath + "/user.ini";
            _loc5_ = Boolean(fileChedk.exists);
            if(_loc5_)
            {
               fileChedk.deleteFile();
            }
            saveuser.open(new File(Core.gamePath + "/user.ini"),"write");
            saveuser.writeUTFBytes(username.text + ":" + password.text + ":" + selectname + ":" + fps);
            saveuser.close();
         }
         else
         {
            _loc4_ = new TextField();
            _loc4_.text = "该账户不存在,请注册";
            _loc4_.textColor = 16711680;
            _loc4_.width = 200;
            _loc4_.height = 20;
            _loc4_.y = this.stage.stageHeight - 60;
            _loc4_.x = 550;
            this.addChild(_loc4_);
         }
      }
   }
}

