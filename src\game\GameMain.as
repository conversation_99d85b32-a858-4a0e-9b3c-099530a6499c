package game
{
	import flash.display.Sprite;
	import flash.events.Event;
	import flash.display.StageAlign;
	import flash.display.StageScaleMode;
	import flash.utils.getDefinitionByName;
	import spark.components.Group;
	import mx.core.UIComponent;

	// 导入AIR适配的Main类
	import game.AIRMain;
	
	/**
	 * 游戏主类 - 用于在AIR环境中包装原有的游戏逻辑
	 * 这个类作为原有Main.as和AIR应用之间的桥梁
	 */
	public class GameMain extends Group
	{
		private var originalMain:Sprite; // 原有的Main类实例
		private var gameContainer:UIComponent; // 用于包装Sprite的容器

		public function GameMain()
		{
			super();
			this.percentWidth = 100;
			this.percentHeight = 100;

			// 创建用于包装Sprite的容器
			gameContainer = new UIComponent();
			gameContainer.percentWidth = 100;
			gameContainer.percentHeight = 100;
			addElement(gameContainer);
		}
		
		/**
		 * 初始化游戏
		 */
		public function initGame():void
		{
			// 等待添加到舞台后再初始化
			if (stage)
			{
				initializeGame();
			}
			else
			{
				addEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
			}
		}
		
		private function onAddedToStage(event:Event):void
		{
			removeEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
			initializeGame();
		}
		
		private function initializeGame():void
		{
			try
			{
				// 设置舞台属性
				stage.align = StageAlign.TOP_LEFT;
				stage.scaleMode = StageScaleMode.NO_SCALE;

				// 创建AIR适配的Main类实例
				originalMain = new AIRMain();
				if (originalMain)
				{
					// 将原有的游戏Sprite添加到UIComponent容器中
					gameContainer.addChild(originalMain);

					// 设置游戏尺寸 - 让游戏自适应容器大小
					originalMain.x = 0;
					originalMain.y = 0;

					trace("游戏主类已成功创建并添加到容器中");
				}
			}
			catch (error:Error)
			{
				trace("初始化游戏时发生错误：" + error.message);
				trace("错误堆栈：" + error.getStackTrace());
			}
		}
		
		/**
		 * 清理资源
		 */
		public function dispose():void
		{
			if (originalMain)
			{
				if (gameContainer && gameContainer.contains(originalMain))
				{
					gameContainer.removeChild(originalMain);
				}

				// 如果Main类有dispose方法，调用它
				if (originalMain.hasOwnProperty("dispose"))
				{
					originalMain["dispose"]();
				}
				originalMain = null;
			}

			if (gameContainer)
			{
				if (this.contains(gameContainer))
				{
					removeElement(gameContainer);
				}
				gameContainer = null;
			}
		}
	}
}
