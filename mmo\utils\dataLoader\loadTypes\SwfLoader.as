package mmo.utils.dataLoader.loadTypes
{
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.utils.ByteArray;
   import mmo.Core;
   import util.Hazyna;
   import util.LiuXiang;
   
   public class SwfLoader extends GroupLoader
   {
      private var loader:Loader;
      
      public function SwfLoader()
      {
         super();
      }
      
      override protected function completeStepHandler(param1:Event) : void
      {
         super.completeStepHandler(param1);
         this.loader = new Loader();
         this.loader.contentLoaderInfo.addEventListener("complete",this.completeHandler);
         this.loader.contentLoaderInfo.addEventListener("ioError",ioErrorHandler);
         var _loc3_:LoaderContext = new LoaderContext(false,ApplicationDomain.currentDomain);
         var _loc2_:ByteArray = urlLoader.data as ByteArray;
         _loc3_.allowCodeImport = true;
         if(Main.isEncrypted(_loc2_))
         {
            Main.getEncryption().decryptBytes(_loc2_,1);
         }
         if(Core.cacheModule == 1)
         {
            if(RWCache.instance().checkFile(this.loadUrl) == false)
            {
               RWCache.instance().addCach(this.loadUrl,_loc2_);
            }
         }
         if(Main.isEncryptedWM(_loc2_))
         {
            this.loader.loadBytes(LiuXiang.decode(_loc2_),_loc3_);
         }
         else
         {
            this.loader.loadBytes(Hazyna.decode(_loc2_),_loc3_);
         }
      }
      
      override protected function completeHandler(param1:Event) : void
      {
         trace("==========================" + this.loadUrl);
         try
         {
            this._data = this.loader.content;
            if(this.loader.content is MovieClip)
            {
               MovieClip(this.loader.content).stop();
            }
            super.completeHandler(param1);
         }
         catch(e:Error)
         {
            trace("SwfLoader::Error:" + e.message);
         }
      }
      
      override public function clear() : void
      {
         super.clear();
         if(this.loader)
         {
            this.loader.contentLoaderInfo.removeEventListener("complete",this.completeHandler);
            this.loader.contentLoaderInfo.removeEventListener("ioError",ioErrorHandler);
            this.loader = null;
         }
      }
   }
}

