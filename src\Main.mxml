<?xml version="1.0" encoding="utf-8"?>
<s:WindowedApplication xmlns:fx="http://ns.adobe.com/mxml/2009"
					   xmlns:s="library://ns.adobe.com/flex/spark"
					   xmlns:mx="library://ns.adobe.com/flex/mx"
					   width="1024" height="768"
					   minWidth="800" minHeight="600"
					   applicationComplete="onApplicationComplete()">

	<fx:Script>
		<![CDATA[
			import game.GameMain;

			private var gameMain:GameMain;

			private function onApplicationComplete():void
			{
				// 创建游戏主类实例
				gameMain = new GameMain();
				// 将游戏添加到AIR窗口中
				addElement(gameMain);
				// 初始化游戏
				gameMain.initGame();
			}
		]]>
	</fx:Script>

	<fx:Declarations>
		<!-- 将非可视元素（例如服务、值对象）放在此处 -->
	</fx:Declarations>
</s:WindowedApplication>
