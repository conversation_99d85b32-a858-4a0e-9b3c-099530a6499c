package display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.FrameLabel;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import sourceManager.SourceManager;
   import util.DisplayHelper;
   
   public class BattleMovieClip extends AbBaseRenderItem
   {
      private static var counter:Number = 0;
      
      public var dispatchEvt:Boolean = true;
      
      protected var _animationName:String = "";
      
      private var _movieClip:MovieClip;
      
      private var _framesLabels:Array;
      
      private var _speed:String;
      
      protected var _loop:Boolean = true;
      
      private var _isFinished:Boolean;
      
      private var _keyFrame:Vector.<uint>;
      
      protected var _reverse:Boolean;
      
      protected var _recalFunction:Function;
      
      protected var _flop:Boolean;
      
      private var _instanceid:uint = 0;
      
      private var _disposed:Boolean;
      
      public function BattleMovieClip(param1:String, param2:Boolean = false)
      {
         super();
         counter++;
         this._instanceid = counter;
         this._animationName = param1;
         this._movieClip = SourceManager.instance.getMovieClipAnimation(param1);
         if(this._movieClip)
         {
            this.addChild(this._movieClip);
            this._framesLabels = this._movieClip.currentLabels;
            this._movieClip.stop();
            this.getAllKeyFrames();
         }
         if(!param2)
         {
            this.mouseChildren = false;
            this.mouseEnabled = false;
         }
         this._movieClip.addFrameScript(this._movieClip.totalFrames - 1,this.onMCPlayComplete);
      }
      
      override public function upDate() : void
      {
         var _loc1_:FrameLabel = null;
         if(this._disposed)
         {
         }
         if(this._movieClip != null)
         {
            if(!this._isFinished || this._loop)
            {
               this._movieClip.gotoAndStop(this._movieClip.currentFrame + 1);
               _loc1_ = this._framesLabels[this._movieClip.currentFrame] as FrameLabel;
               if(this._keyFrame.length && this._keyFrame.indexOf(this._movieClip.currentFrame) >= 0)
               {
                  if(this.dispatchEvt)
                  {
                     this.dispatchEvent(new Event(this.EVENT_PRE_END));
                     this.dispatchEvent(new Event("sprite_aniamtiin_key_frame"));
                  }
               }
            }
         }
      }
      
      override public function getRec() : Rectangle
      {
         if(this._movieClip)
         {
            return this._movieClip.getBounds(this._movieClip);
         }
         return null;
      }
      
      override public function resizeAnimation(param1:uint, param2:uint) : void
      {
         if(this._movieClip)
         {
            this._movieClip.width = param1;
            this._movieClip.height = param2;
         }
      }
      
      override public function getMCCurrentFrameBitmap() : Bitmap
      {
         var _loc1_:BitmapData = null;
         var _loc2_:Bitmap = new Bitmap();
         if(this._movieClip)
         {
            _loc1_ = this.drawMC();
         }
         else
         {
            _loc1_ = this.drawMC();
         }
         _loc2_.bitmapData = _loc1_;
         return _loc2_;
      }
      
      override public function resetAnimation() : void
      {
         if(this._movieClip)
         {
            this._movieClip.gotoAndStop(1);
         }
      }
      
      override public function dispose() : void
      {
         this._disposed = true;
         if(this._movieClip)
         {
            this._movieClip.addFrameScript(this._movieClip.totalFrames - 1,null);
            this._movieClip.gotoAndStop(1);
            DisplayHelper.removeFromStage(this._movieClip);
            SourceManager.instance.cacheItem(this._movieClip);
         }
         this._framesLabels = [];
         super.dispose();
      }
      
      private function drawMC() : BitmapData
      {
         var _loc5_:int = Math.ceil(this._movieClip.width);
         var _loc4_:int = Math.ceil(this._movieClip.height);
         var _loc2_:Rectangle = this.getRec();
         var _loc3_:Matrix = new Matrix();
         _loc3_.tx = -_loc2_.x + 2;
         _loc3_.ty = -_loc2_.y + 1;
         var _loc1_:BitmapData = new BitmapData(_loc5_,_loc4_,true,0);
         _loc1_.draw(this._movieClip,_loc3_);
         return _loc1_;
      }
      
      private function getAllKeyFrames() : void
      {
         var _loc4_:FrameLabel = this._framesLabels[this._movieClip.currentFrame] as FrameLabel;
         if(_loc4_ && this._movieClip.currentFrame <= 1 && _loc4_.name.indexOf("speed") >= 0)
         {
            this._speed = _loc4_.name;
         }
         this._keyFrame = new Vector.<uint>();
         var _loc3_:uint = uint(this._movieClip.totalFrames);
         var _loc1_:uint = this._framesLabels.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc1_)
         {
            _loc4_ = this._framesLabels[_loc2_] as FrameLabel;
            if(_loc4_ && _loc4_.name && _loc4_.name.indexOf("sound") < 0)
            {
               this._keyFrame.push(_loc4_.frame);
            }
            _loc2_++;
         }
         if(this._keyFrame.length == 0)
         {
            this._keyFrame.push(4);
         }
      }
      
      private function onMCPlayComplete() : void
      {
         if(!this._loop)
         {
            this._isFinished = true;
            this._movieClip.gotoAndStop(1);
            if(this._recalFunction != null)
            {
               this._recalFunction();
               this._recalFunction = null;
            }
         }
         else
         {
            this.resetAnimation();
         }
         if(this.dispatchEvt)
         {
            this.dispatchEvent(new Event(this.EVENT_END));
         }
      }
      
      override public function get currentFrame() : uint
      {
         if(this._movieClip)
         {
            return this._movieClip.currentFrame;
         }
         return 0;
      }
      
      override public function get totalFrames() : uint
      {
         if(this._movieClip)
         {
            return this._movieClip.totalFrames;
         }
         return 0;
      }
      
      override public function get currentBitmapPositon() : Point
      {
         var _loc2_:Point = new Point();
         var _loc1_:Rectangle = this.getRec();
         if(_loc1_)
         {
            _loc2_.x = _loc1_.x;
            _loc2_.y = _loc1_.y;
         }
         return _loc2_;
      }
      
      override public function get currentBitmapWidth() : uint
      {
         var _loc1_:Rectangle = this.getRec();
         return !!_loc1_ ? uint(_loc1_.width) : uint(this.width);
      }
      
      override public function get currentBitmapHeight() : uint
      {
         var _loc1_:Rectangle = this.getRec();
         return !!_loc1_ ? uint(_loc1_.height) : uint(this.height);
      }
      
      override public function get keyFrameNumber() : uint
      {
         if(this._keyFrame)
         {
            return this._keyFrame.length;
         }
         return 0;
      }
      
      override public function get EVENT_PRE_END() : String
      {
         return "BASE_ACTION_PRE_END";
      }
      
      override public function get EVENT_END() : String
      {
         return "BASE_ACTION_END";
      }
      
      override public function get loop() : Boolean
      {
         return this._loop;
      }
      
      override public function set loop(param1:Boolean) : void
      {
         this._loop = param1;
      }
      
      override public function set reverse(param1:Boolean) : void
      {
         this._reverse = param1;
      }
      
      override public function get speed() : String
      {
         return this._speed;
      }
      
      override public function get flop() : Boolean
      {
         return this._flop;
      }
      
      override public function set flop(param1:Boolean) : void
      {
         this._flop = param1;
      }
   }
}

